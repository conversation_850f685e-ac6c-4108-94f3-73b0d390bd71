# 文件存储后端动态配置更新

## 更新概述

本次更新为Django项目添加了通过环境变量动态控制文件存储后端的功能，支持在阿里云OSS存储和本地文件存储之间灵活切换。

## 主要变更

### 1. 配置文件修改 (`config/settings.py`)

- 添加了 `FILE_STORAGE_BACKEND` 环境变量支持
- 实现了动态存储后端选择逻辑
- 保持静态文件存储配置不变

**修改的配置段：**

<augment_code_snippet path="config/settings.py" mode="EXCERPT">
````python
# 文件存储配置
# 根据环境变量 FILE_STORAGE_BACKEND 动态选择存储后端
# 可选值：
# - "oss" 或 "aliyun": 使用阿里云OSS存储
# - "local" 或其他值: 使用Django默认本地文件存储
FILE_STORAGE_BACKEND = os.getenv("FILE_STORAGE_BACKEND", "local").lower()

# 动态配置存储后端
if FILE_STORAGE_BACKEND in ["oss", "aliyun"]:
    # 使用阿里云OSS存储
    DEFAULT_FILE_STORAGE_BACKEND = "extensions.storage.CustomAliyunOSSStorage"
else:
    # 使用Django默认本地文件存储
    DEFAULT_FILE_STORAGE_BACKEND = "django.core.files.storage.FileSystemStorage"

STORAGES = {
    "default": {
        "BACKEND": DEFAULT_FILE_STORAGE_BACKEND,
    },
    "staticfiles": {
        "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
    },
}
````
</augment_code_snippet>

### 2. 环境配置文件更新

**开发环境 (`.env.develop`):**
- 添加 `FILE_STORAGE_BACKEND=local` 配置
- 开发环境默认使用本地存储

**生产环境 (`.env.product`):**
- 添加 `FILE_STORAGE_BACKEND=oss` 配置
- 生产环境默认使用阿里云OSS存储

### 3. 新增文档

- `docs/FILE_STORAGE_CONFIG.md`: 详细的配置说明文档
- `docs/STORAGE_BACKEND_UPDATE.md`: 本次更新的总结文档

### 4. 测试脚本

- `scripts/test_storage_config.py`: 用于验证存储配置的测试脚本

## 环境变量说明

### FILE_STORAGE_BACKEND

**功能：** 控制文件上传的存储后端选择

**可选值：**
- `oss` 或 `aliyun`: 使用阿里云OSS存储
- `local` 或其他值: 使用Django默认本地文件存储

**默认值：** `local`

**示例：**
```bash
# 使用阿里云OSS
FILE_STORAGE_BACKEND=oss

# 使用本地存储
FILE_STORAGE_BACKEND=local
```

## 使用方法

### 1. 开发环境配置

在 `.env.develop` 文件中：
```bash
FILE_STORAGE_BACKEND=local
```

### 2. 生产环境配置

在 `.env.product` 文件中：
```bash
FILE_STORAGE_BACKEND=oss

# 确保阿里云OSS相关配置正确
ALIYUN_OSS_ACCESS_KEY_ID=your_access_key_id
ALIYUN_OSS_ACCESS_KEY_SECRET=your_access_key_secret
ALIYUN_OSS_BUCKET_NAME=your_bucket_name
ALIYUN_OSS_ENDPOINT=https://oss-cn-qingdao.aliyuncs.com
```

### 3. 测试配置

运行测试脚本验证配置：
```bash
python scripts/test_storage_config.py
```

## 兼容性说明

- **向后兼容：** 如果不设置 `FILE_STORAGE_BACKEND` 环境变量，系统默认使用本地存储
- **静态文件不受影响：** 该配置只影响用户上传的文件，静态文件仍使用原有配置
- **现有代码无需修改：** 所有使用 `default_storage` 的代码都会自动使用新的配置

## 注意事项

1. **配置验证：** 使用阿里云OSS时，请确保所有相关环境变量都已正确配置
2. **文件迁移：** 更改存储后端时，已上传的文件不会自动迁移
3. **权限检查：** 确保阿里云OSS账号有足够的权限进行文件操作
4. **网络连接：** 使用OSS时需要确保服务器能够访问阿里云服务

## 故障排除

如果遇到问题，请：

1. 检查环境变量是否正确设置
2. 运行测试脚本验证配置
3. 查看Django日志获取详细错误信息
4. 参考 `docs/FILE_STORAGE_CONFIG.md` 获取更多帮助

## 相关文件

- `config/settings.py`: 主要配置文件
- `.env.develop`: 开发环境配置
- `.env.product`: 生产环境配置
- `docs/FILE_STORAGE_CONFIG.md`: 详细配置说明
- `scripts/test_storage_config.py`: 测试脚本
- `extensions/storage.py`: 自定义阿里云OSS存储类
