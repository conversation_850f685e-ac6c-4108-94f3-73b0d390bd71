"""

Django settings for config project.



Generated by 'django-admin startproject' using Django 5.1.3.



For more information on this file, see

https://docs.djangoproject.com/en/5.1/topics/settings/



For the full list of settings and their values, see

https://docs.djangoproject.com/en/5.1/ref/settings/

"""

import os
import sys
from datetime import timedelta
from pathlib import Path

from dotenv import load_dotenv

# 加载.env文件
load_dotenv()

# 构建到 .env 文件的路径
ENV_PATH = Path(__file__).resolve().parent.parent / ".env"

# 根据 DJANGO_ENV 环境变量加载对应的 .env 文件
django_env = os.getenv("DJANGO_ENV", "develop")
load_dotenv(ENV_PATH.with_name(f".env.{django_env}"))

# 构建项目路径，如：BASE_DIR / 'subdir'。
BASE_DIR = Path(__file__).resolve().parent.parent
sys.path.insert(0, os.path.join(BASE_DIR, "apps"))

# 在 settings.py 的 BASE_DIR 定义之后添加
LOGS_DIR = os.path.join(BASE_DIR, "logs")

# 确保日志目录存在
os.makedirs(LOGS_DIR, exist_ok=True)

# 快速开发设置 - 不适合生产环境

# 参见 https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/


# 安全警告：请在生产环境中保持密钥的秘密性！

SECRET_KEY = os.getenv("SECRET_KEY", "django-insecure-okzqeba*9fy&(5x5=3c0s(d2w_)y%@4zj$0vsz3#vz!7#g-fd2")

# 安全警告：不要在生产环境中开启调试模式！

DEBUG = os.getenv("DEBUG") == "True"

# 允许的主机列表
# 从环境变量读取，如果未设置则使用默认值
ALLOWED_HOSTS_ENV = os.getenv("ALLOWED_HOSTS", "")
if ALLOWED_HOSTS_ENV:
    # 如果环境变量存在，则按逗号分割并去除空白字符
    ALLOWED_HOSTS = [host.strip() for host in ALLOWED_HOSTS_ENV.split(",") if host.strip()]
else:
    # 如果环境变量不存在，则使用默认值
    ALLOWED_HOSTS = [
        "virusht.sierting.com",
        "virusqt.sierting.com",
        "localhost",
        "127.0.0.1",
        "www.fanglesuo.cn",
        "fanglesuo.cn",
        "admin.fanglesuo.cn",
        "***********",
        "***********"
    ]

# 应用程序定义


# 安装的应用程序列表

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "rest_framework",  # RESTful API
    "djoser",  # 用户认证
    "drf_spectacular",  # API文档
    "channels",  # WebSocket支持
    "django_filters",  # 过滤器
    "corsheaders",  # CORS支持
    "django5_aliyun_oss",
    "rest_access_policy",
    "django_q",  # 任务队列
    # 自定义应用
    "apps.users",
    "apps.dashboard",
    "apps.infection",
    "apps.virus",
    "apps.assets",
    "apps.exercise",
    "apps.system",
    "apps.phishing",
    "apps.family",
    "apps.chat",
]

# 中间件列表

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",  # 添加whitenoise中间件
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",  # CORS中间件，必须在CommonMiddleware之前
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "django.middleware.gzip.GZipMiddleware",  # 添加GZip压缩
]

# 根URL配置

ROOT_URLCONF = "config.urls"

# 模板配置

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR / "templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

# WSGI应用程序

WSGI_APPLICATION = "config.wsgi.application"

# ASGI应用程序

ASGI_APPLICATION = "config.asgi.application"

# 数据库配置

# 参见 https://docs.djangoproject.com/en/5.1/ref/settings/#databases


# DATABASES = {
#     "default": {
#         "ENGINE": "django.db.backends.sqlite3",
#         "NAME": BASE_DIR / "db.sqlite3",
#     }
# }

# postgresql数据库配置
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": os.getenv("DB_NAME"),
        "USER": os.getenv("DB_USER"),
        "PASSWORD": os.getenv("DB_PASSWORD"),
        "HOST": os.getenv("DB_HOST"),
        "PORT": os.getenv("DB_PORT"),
    }
}

# 密码验证器配置

# 参见 https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators


AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# 国际化设置
LANGUAGE_CODE = "zh-hans"
TIME_ZONE = "Asia/Shanghai"
USE_I18N = True
USE_TZ = False

# 静态文件配置 (CSS, JavaScript, Images)

# 参见 https://docs.djangoproject.com/en/5.1/howto/static-files/


# 静态文件URL前缀

STATIC_URL = "/static/"

STATIC_ROOT = BASE_DIR / "static"

# 媒体文件URL前缀

MEDIA_URL = "/media/"

MEDIA_ROOT = BASE_DIR / "media"

# 默认主键字段类

# 参见 https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field


DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# DRF配置
REST_FRAMEWORK = {
    "DEFAULT_SCHEMA_CLASS": "drf_spectacular.openapi.AutoSchema",
    "DEFAULT_AUTHENTICATION_CLASSES": (
        "rest_framework_simplejwt.authentication.JWTAuthentication",
        "rest_framework.authentication.SessionAuthentication",
        "rest_framework.authentication.BasicAuthentication",
    ),
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.IsAuthenticated",
    ],
    "DEFAULT_PAGINATION_CLASS": "extensions.pagination.CustomPagination",
    "PAGE_SIZE": 10,
    "DEFAULT_FILTER_BACKENDS": [
        "django_filters.rest_framework.DjangoFilterBackend",
        "rest_framework.filters.SearchFilter",
        "rest_framework.filters.OrderingFilter",
    ],
    "DEFAULT_VERSIONING_CLASS": "rest_framework.versioning.URLPathVersioning",
    "DEFAULT_VERSION": "v1",
    "ALLOWED_VERSIONS": ["v1"],
    "VERSION_PARAM": "version",
    "EXCEPTION_HANDLER": "apps.users.utils.custom_exception_handler",
    'DATETIME_FORMAT': "%Y-%m-%d %H:%M:%S",
    'DATE_FORMAT': "%Y-%m-%d",
    'TIME_FORMAT': "%H:%M:%S",
    # 禁用浏览器API界面
    "DEFAULT_RENDERER_CLASSES": [
        "rest_framework.renderers.JSONRenderer",
    ],
}

# JWT设置
SIMPLE_JWT = {
    "AUTH_HEADER_TYPES": ("Bearer",),
    "ACCESS_TOKEN_LIFETIME": timedelta(days=1),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=7),
    "ROTATE_REFRESH_TOKENS": True,
    "BLACKLIST_AFTER_ROTATION": True,
    "UPDATE_LAST_LOGIN": True,
}

# DRF Spectacular settings
SPECTACULAR_SETTINGS = {
    "TITLE": "思而听防勒索病毒模拟演练平台 API",
    "DESCRIPTION": "思而听防勒索病毒模拟演练平台后端API文档",
    "VERSION": "1.0.0",
    "SCHEMA_PATH_PREFIX": "/api/v1",
}

# 自定义用户模型

AUTH_USER_MODEL = "users.User"

# Djoser配置
DJOSER = {
    "SERIALIZERS": {
        "user_create": "apps.users.serializers.UserCreateSerializer",
        "user": "apps.users.serializers.UserSerializer",
        "current_user": "apps.users.serializers.UserSerializer",
    },
    "PERMISSIONS": {
        "user": ["rest_framework.permissions.IsAuthenticated"],
        "user_list": ["rest_framework.permissions.IsAdminUser"],
    },
    "PASSWORD_RESET_CONFIRM_URL": "#/password/reset/confirm/{uid}/{token}",
    "USERNAME_RESET_CONFIRM_URL": "#/username/reset/confirm/{uid}/{token}",
    "ACTIVATION_URL": "#/activate/{uid}/{token}",
    "SEND_ACTIVATION_EMAIL": False,
    "TOKEN_MODEL": None,
}

# 系统配置
SYSTEM_CONFIG = {
    # 日志保留时间（天）
    "LOG_RETENTION_DAYS": 30,
    # 系统监控阈值
    "MONITOR_THRESHOLDS": {
        "CPU_PERCENT": 80,  # CPU使用率告警阈值
        "MEMORY_PERCENT": 85,  # 内存使用率告警阈值
        "DISK_PERCENT": 90,  # 磁盘使用率告警阈值
    },
    # 邮件通知设置
    "EMAIL_NOTIFICATION": {
        "ENABLED": True,  # 是否启用邮件通知
        "ALERT_INTERVAL": 3600,  # 告警间隔（秒）
        "DAILY_REPORT_TIME": "08:00",  # 每日报告时间
    },
}

# 添加Channels配置
CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels.layers.InMemoryChannelLayer"
    }
}

# CSRF配置
# 从环境变量读取，如果未设置则使用默认值
CSRF_TRUSTED_ORIGINS_ENV = os.getenv("CSRF_TRUSTED_ORIGINS", "")
if CSRF_TRUSTED_ORIGINS_ENV:
    # 如果环境变量存在，则按逗号分割并去除空白字符
    CSRF_TRUSTED_ORIGINS = [origin.strip() for origin in CSRF_TRUSTED_ORIGINS_ENV.split(",") if origin.strip()]
else:
    # 如果环境变量不存在，则使用默认值（同时支持HTTP和HTTPS）
    CSRF_TRUSTED_ORIGINS = [
        "http://virusht.sierting.com",
        "https://virusht.sierting.com",
        "http://virusqt.sierting.com",
        "https://virusqt.sierting.com",
        "http://www.fanglesuo.cn",
        "https://www.fanglesuo.cn",
        "http://fanglesuo.cn",
        "https://fanglesuo.cn",
        "http://admin.fanglesuo.cn",
        "https://admin.fanglesuo.cn",
    ]

# CORS配置
# 从环境变量读取，如果未设置则使用默认值
CORS_ALLOW_ALL_ORIGINS = False  # 不允许所有源
CORS_ALLOW_CREDENTIALS = True  # 允许携带cookie
CORS_ORIGIN_ALLOW_ALL = False  # 使用白名单

# 从环境变量读取允许的源
CORS_ALLOWED_ORIGINS_ENV = os.getenv("CORS_ALLOWED_ORIGINS", "")
if CORS_ALLOWED_ORIGINS_ENV:
    # 如果环境变量存在，则按逗号分割并去除空白字符
    CORS_ALLOWED_ORIGINS = [origin.strip() for origin in CORS_ALLOWED_ORIGINS_ENV.split(",") if origin.strip()]
else:
    # 如果环境变量不存在，则使用默认值（同时支持HTTP和HTTPS）
    CORS_ALLOWED_ORIGINS = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://virusht.sierting.com",
        "https://virusht.sierting.com",
        "http://virusqt.sierting.com",
        "https://virusqt.sierting.com",
        "http://www.fanglesuo.cn",
        "https://www.fanglesuo.cn",
        "http://fanglesuo.cn",
        "https://fanglesuo.cn",
        "http://admin.fanglesuo.cn",
        "https://admin.fanglesuo.cn",
    ]

# 保留正则表达式配置用于动态端口匹配
CORS_ALLOWED_ORIGIN_REGEXES = [
    r"^http://10\.0\.100\.47:\d+$",
]

CORS_ALLOW_METHODS = [
    "DELETE",
    "GET",
    "OPTIONS",
    "PATCH",
    "POST",
    "PUT",
]
CORS_ALLOW_HEADERS = [
    "accept",
    "accept-encoding",
    "authorization",
    "content-type",
    "dnt",
    "origin",
    "user-agent",
    "x-csrftoken",
    "x-requested-with",
]

ALIYUN_OSS = {
    "ACCESS_KEY_ID": os.environ.get("ALIYUN_OSS_ACCESS_KEY_ID"),
    "ACCESS_KEY_SECRET": os.environ.get("ALIYUN_OSS_ACCESS_KEY_SECRET"),
    "ENDPOINT": os.environ.get("ALIYUN_OSS_ENDPOINT"),
    "BUCKET_NAME": os.environ.get("ALIYUN_OSS_BUCKET_NAME"),
    "URL_EXPIRE_SECONDS": 3600,  # 可选，默认为3600
}

# 设置为默认存储器
STORAGES = {
    "default": {
        "BACKEND": "extensions.storage.CustomAliyunOSSStorage",
    },
    "staticfiles": {
        "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
    },
}

# Redis配置
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": os.environ.get("REDIS_URL"),
        "OPTIONS": {
            "PASSWORD": os.environ.get("REDIS_PASSWORD"),
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "SERIALIZER_CLASS": "django_redis.serializers.PickleSerializer",
            "SOCKET_TIMEOUT": 5,
            "SOCKET_CONNECT_TIMEOUT": 10,
        },
    }
}

# 阿里云短信配置
ALIYUN_SMS = {
    "ACCESS_KEY_ID": os.getenv('ALIYUN_SMS_ACCESS_KEY_ID'),
    "ACCESS_KEY_SECRET": os.getenv('ALIYUN_SMS_ACCESS_KEY_SECRET'),
    "SIGN_NAME": os.getenv('ALIYUN_SMS_SIGN_NAME'),
    "TEMPLATE_CODE": os.getenv('ALIYUN_SMS_TEMPLATE_CODE'),
    "REGION_ID": os.getenv('ALIYUN_SMS_REGION_ID'),
}

# 木马服务器地址
TROJAN_SERVER_URL = os.getenv('TROJAN_SERVER_URL')

# Coze AI配置
AI_CONFIG = {
    "auth_type": os.environ.get("AI_AUTH_TYPE", "token"),  # 认证类型: token或jwt
    "access_token": os.environ.get("AI_ACCESS_TOKEN"),  # Coze个人访问令牌(PAT)
    "bot_id": os.environ.get("AI_BOT_ID"),  # Coze Bot ID
    # OAuth JWT认证相关配置
    "jwt_client_id": os.environ.get("AI_JWT_CLIENT_ID"),  # OAuth应用的客户端ID
    "jwt_private_key": os.environ.get("AI_JWT_PRIVATE_KEY"),  # 私钥内容
    "jwt_public_key_id": os.environ.get("AI_JWT_PUBLIC_KEY_ID"),  # 公钥指纹
}

# 大模型配置
DASHSCOPE_API_KEY = os.environ.get("DASHSCOPE_API_KEY")
AI_MODEL = os.environ.get("AI_MODEL")
AI_API_KEY = os.environ.get("AI_API_KEY")

# Django Q2配置
Q_CLUSTER = {
    'name': 'virus_sierting', # 集群名称
    'workers': 4, # 工作进程数
    'recycle': 500, # 回收时间
    'timeout': 60, # 超时时间
    'compress': True, # 压缩
    'save_limit': 250, # 保存限制
    'queue_limit': 500, # 队列限制
    'cpu_affinity': 1, # CPU亲和性
    'label': 'Django Q2', # 标签
    'redis': {
        'host': os.getenv('REDIS_HOST', 'localhost'), # Redis主机
        'port': int(os.getenv('REDIS_PORT', 6379)), # Redis端口
        'password': os.getenv('REDIS_PASSWORD'), # Redis密码
        'db': 2, # Redis数据库
    }
}

# Whitenoise配置
STATICFILES_STORAGE = "whitenoise.storage.CompressedManifestStaticFilesStorage"
WHITENOISE_MAX_AGE = 31536000  # 1年的缓存时间

# 安全相关配置
# 注意: HTTPS重定向和HSTS由服务器(如Nginx)处理
# 这里只配置应用级别的安全设置

# 如果在代理后运行，这个设置可以让Django知道请求是通过HTTPS发送的
# 为了同时支持HTTP和HTTPS，我们保留此设置但不强制HTTPS
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')

# Cookie安全设置 - 支持HTTP和HTTPS
# 为了同时支持HTTP和HTTPS访问，将Cookie安全设置为False
SESSION_COOKIE_SECURE = False
CSRF_COOKIE_SECURE = False

# 始终启用的安全设置
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'DENY'  # 阻止在iframe中嵌入

# 会话安全设置
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Lax'
CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SAMESITE = 'Lax'

# 日志记录
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {  # 详细
            'format': '%(levelname)s %(asctime)s %(module)s %(process)d %(thread)d %(message)s'
        },
        'standard': {  # 标准
            'format': '[%(asctime)s] [%(levelname)s] %(message)s'
        },
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'stream': 'ext://sys.stdout',
            'formatter': 'standard'
        },
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(LOGS_DIR, 'debug.log'),
            'formatter': 'standard'
        },
        'default': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(LOGS_DIR, 'default.log'),  # 日志输出文件
            'maxBytes': 1024 * 1024 * 5,  # 文件大小
            'backupCount': 5,  # 备份份数
            'formatter': 'standard',  # 使用哪种formatters日志格式
        },
    },
    'loggers': {  # log记录器，配置之后就会对应的输出日志
        'django': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.request ': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'django.db.backends': {
            'handlers': ['file', 'console'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}

