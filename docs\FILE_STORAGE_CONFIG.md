# 文件存储配置说明

## 概述

本项目支持通过环境变量动态配置文件存储后端，可以在阿里云OSS存储和本地文件存储之间切换。

## 环境变量配置

### FILE_STORAGE_BACKEND

控制文件上传的存储后端选择。

**可选值：**
- `oss` 或 `aliyun`: 使用阿里云OSS存储
- `local` 或其他值: 使用Django默认本地文件存储

**默认值：** `local`

## 配置示例

### 1. 使用阿里云OSS存储

在 `.env` 文件中添加：

```bash
# 设置存储后端为阿里云OSS
FILE_STORAGE_BACKEND=oss

# 阿里云OSS配置（使用OSS时必须配置）
ALIYUN_OSS_ACCESS_KEY_ID=your_access_key_id
ALIYUN_OSS_ACCESS_KEY_SECRET=your_access_key_secret
ALIYUN_OSS_BUCKET_NAME=your_bucket_name
ALIYUN_OSS_ENDPOINT=https://oss-cn-qingdao.aliyuncs.com
```

### 2. 使用本地文件存储

在 `.env` 文件中添加：

```bash
# 设置存储后端为本地存储
FILE_STORAGE_BACKEND=local
```

或者不设置该环境变量（默认使用本地存储）。

## 存储后端说明

### 阿里云OSS存储

- **优点：**
  - 高可用性和可扩展性
  - 支持CDN加速
  - 自动备份和容灾
  - 适合生产环境

- **配置要求：**
  - 需要配置阿里云OSS相关环境变量
  - 需要有效的阿里云OSS账号和权限

- **文件访问：**
  - 文件通过阿里云OSS的公网URL访问
  - 支持HTTPS访问

### 本地文件存储

- **优点：**
  - 配置简单
  - 无需外部依赖
  - 适合开发和测试环境

- **限制：**
  - 文件存储在服务器本地
  - 不支持分布式部署
  - 需要定期备份

- **文件访问：**
  - 文件通过Django的MEDIA_URL访问
  - 路径：`/media/文件路径`

## 环境配置文件示例

### 开发环境 (.env.develop)

```bash
# 开发环境使用本地存储
FILE_STORAGE_BACKEND=local

# 其他配置...
DEBUG=True
DB_NAME=RansomSimDB
# ...
```

### 生产环境 (.env.product)

```bash
# 生产环境使用阿里云OSS
FILE_STORAGE_BACKEND=oss

# 阿里云OSS配置
ALIYUN_OSS_ACCESS_KEY_ID=LTAI5tJYTv3aiUc86xvzaXuw
ALIYUN_OSS_ACCESS_KEY_SECRET=******************************
ALIYUN_OSS_BUCKET_NAME=ransomware-emergency
ALIYUN_OSS_ENDPOINT=https://oss-cn-qingdao.aliyuncs.com

# 其他配置...
DEBUG=False
# ...
```

## 注意事项

1. **静态文件存储不受影响：** 该配置只影响用户上传的文件，静态文件（CSS、JS等）仍使用Django默认的静态文件存储。

2. **环境变量优先级：** 环境变量的值不区分大小写，`OSS`、`oss`、`ALIYUN`、`aliyun` 都会被识别为阿里云OSS存储。

3. **配置验证：** 使用阿里云OSS时，请确保所有相关的环境变量都已正确配置，否则文件上传可能失败。

4. **文件迁移：** 更改存储后端时，已上传的文件不会自动迁移，需要手动处理。

## 测试配置

可以通过以下方式测试当前的存储配置：

1. 启动Django应用
2. 查看日志中的存储后端信息
3. 尝试上传文件并检查文件是否正确保存
4. 验证文件URL是否可以正常访问

## 故障排除

### 阿里云OSS配置问题

如果使用阿里云OSS时遇到问题，请检查：

1. 环境变量是否正确设置
2. 阿里云OSS账号权限是否充足
3. 网络连接是否正常
4. Bucket是否存在且可访问

### 本地存储问题

如果使用本地存储时遇到问题，请检查：

1. MEDIA_ROOT目录是否存在且有写权限
2. 磁盘空间是否充足
3. 文件路径是否正确
